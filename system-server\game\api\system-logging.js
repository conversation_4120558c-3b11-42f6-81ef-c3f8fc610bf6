const axios = require("axios");
const { app_url } = require("../../config");

async function logNFTDistribution(nftCount, uniqueWaxIds, dailyLimit, percentReached) {
  const message = `${nftCount} NFTs minted to ${uniqueWaxIds} unique WAX IDs. ${nftCount}/${dailyLimit} NFTs minted today. Reached ${percentReached}% of 24-hour NFT reward limit.`;

  const details = {
    unique_wax_ids: uniqueWaxIds,
    total_minted: nftCount,
    daily_limit: dailyLimit,
    percent_reached: percentReached
  };

  await addSystemLog('INFO', 'REWARD', 'NFT', message, details);
}

async function logDUSTDistribution(dustAmount, uniqueWaxIds, dailyLimit, percentReached) {
  const message = `${dustAmount} DUST distributed to ${uniqueWaxIds} unique WAX IDs. ${dustAmount}/${dailyLimit} DUST rewards issued today. Reached ${percentReached}% of 24-hour DUST reward limit.`;

  const details = {
    unique_wax_ids: uniqueWaxIds,
    total_distributed: dustAmount,
    daily_limit: dailyLimit,
    percent_reached: percentReached
  };

  await addSystemLog('INFO', 'REWARD', 'DUST', message, details);
}

async function logGXPDistribution(gxpAmount, uniqueWaxIds, dailyLimit, percentReached) {
  const message = `${gxpAmount} GXP distributed to ${uniqueWaxIds} unique WAX IDs. Reached ${percentReached}% of 24-hour GXP reward limit.`;

  const details = {
    unique_wax_ids: uniqueWaxIds,
    total_distributed: gxpAmount,
    daily_limit: dailyLimit,
    percent_reached: percentReached
  };

  await addSystemLog('INFO', 'REWARD', 'GXP', message, details);
}

async function logLimitReached(rewardType, amount, uniqueWaxIds, dailyLimit, pendingWaxIds) {
  const message = `${amount} ${rewardType} distributed to ${uniqueWaxIds} unique WAX IDs. ${amount}/${dailyLimit} ${rewardType} rewards issued today. Reached 100% of 24-hour ${rewardType} reward limit. ${pendingWaxIds} WAX IDs await disbursal after 24-hour limit reset.`;

  const details = {
    unique_wax_ids: uniqueWaxIds,
    total_distributed: amount,
    daily_limit: dailyLimit,
    percent_reached: 100,
    pending_wax_ids: pendingWaxIds
  };

  await addSystemLog('WARNING', 'LIMIT', rewardType, message, details);
}

async function logError(rewardType, errorCount, errorCode, errorMessage) {
  const message = `${errorCount} errors occurred while attempting ${rewardType} rewards issued CRITICAL ALERT! [${errorCode}: ${errorMessage}]`;

  const details = {
    error_count: errorCount,
    error_code: errorCode,
    error_details: errorMessage
  };

  await addSystemLog('ERROR', 'REWARD', rewardType, message, details);
}

async function addSystemLog(msgType, category, rewardType, message, details) {
  try {
    const payload = {
      msg_type: msgType,
      category: category,
      reward_type: rewardType,
      message: message,
      details: details
    };

    const response = await axios.post(
      `${app_url}/system-logs`,
      payload,
      {
        headers: {
          "Content-Type": "application/json",
          'x-system-operation': 'true',
          'User-Agent': 'system-server-logging'
        },
      }
    );

    console.log(`System log added: ${message}`);
  } catch (error) {
    console.error("Error adding system log:", error);
  }
}

module.exports = {
  logNFTDistribution,
  logDUSTDistribution,
  logGXPDistribution,
  logLimitReached,
  logError,
  addSystemLog
};