// Wait for DOM to be fully loaded before initializing
document.addEventListener('DOMContentLoaded', async function() {
  try {
    // Set up login event listener after DOM is ready
    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn) {
      loginBtn.addEventListener('click', login);
    } else {
      console.error('Login button not found');
    }

    await getMapData();
    // Only display worlds after data is loaded and DOM is ready
    await displayWorlds();
  } catch (error) {
    console.error('Error initializing map:', error);
  }
});

