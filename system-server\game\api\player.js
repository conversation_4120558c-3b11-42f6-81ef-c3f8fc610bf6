// shared/playerBalances.js
const axios = require("axios");
const { app_url } = require("../../config");

async function getPlayerBalances() {
  var url = `${app_url}/players/`;
  try {
    const response = await axios.get(url, {
      headers: {
        'x-system-operation': 'true',
        'User-Agent': 'system-server-player-api'
      }
    });
    if (response.data) {
      var balancesData = response.data;
      var playerBalances = {};
      for (var i = 0; i < balancesData.length; i++) {
        var playerData = {};
        var wax_id = balancesData[i].wax_id;
        playerData.gxp = Number(balancesData[i].gxp);
        playerData.nectar = balancesData[i].nectar;
        playerData.credits = balancesData[i].credits;
        playerData.lv = balancesData[i].lv;
        playerData.xp = Number(balancesData[i].xp);
        playerData.joinDate = balancesData[i].date_joined;
        playerData.lastOnline = balancesData[i].last_online;
        playerBalances[wax_id] = playerData;
      }
      console.log(playerBalances);
      return playerBalances;
    } else {
      console.log("No player data found");
      return null;
    }
  } catch (error) {
    console.log("Error occurred while fetching player data:", error);
    return null;
  }
}

module.exports = { getPlayerBalances };
