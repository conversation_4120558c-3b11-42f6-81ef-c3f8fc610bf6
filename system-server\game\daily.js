// The general.js and daily.js are files run on a seperate heroku instance for systems operations that occur away from the clients
// access. Treat the folder 'system-server' files as belonging to a seperate app from the main app.
const axios = require("axios");
const { app_url } = require("../config");

var allPlayers = {};
var playersNeedingMoreCredits = [];
var playersNeedingMoreNectar = [];

async function processDaily() {
  var url = `${app_url}/players/`;
  axios.get(url, {
    headers: {
      'x-system-operation': 'true',
      'User-Agent': 'system-server-daily-cron'
    }
  }).then((response) => {
    allPlayers = response.data;
    for (m in allPlayers) {
      var wax = allPlayers[m].wax_id;
      var nectar = allPlayers[m].nectar;
      var credits = allPlayers[m].credits;
      if (credits < 3) {
        playersNeedingMoreCredits.push([wax, credits]);
      }
      if (nectar < 3) {
        playersNeedingMoreNectar.push([wax, nectar]);
      }
    }
    for (s in playersNeedingMoreCredits) {
      updatePlayerCredits(playersNeedingMoreCredits[s][0], 3);
    }
    for (u in playersNeedingMoreNectar) {
      updatePlayerNectar(playersNeedingMoreNectar[u][0], 3);
    }
  }, (error) => {
    console.log(error);
  });
}

async function updatePlayerCredits(wax_id, amt) {
  var url = `${app_url}/players/credits/` + wax_id;
  let change = {
    "credits": amt
  };
  axios.put(url, change, {
    headers: {
      "Content-Type": "application/json",
      'x-system-operation': 'true',
      'User-Agent': 'system-server-daily-cron'
    }
  }).then(() => {
    console.log('success');
  }).catch(error => {
    console.error('Error updating player credits:', error);
  });
}

async function updatePlayerNectar(wax_id, amt) {
  var url = `${app_url}/players/nectar/` + wax_id;
  let change = {
    "nectar": amt
  };
  axios.put(url, change, {
    headers: {
      "Content-Type": "application/json",
      'x-system-operation': 'true',
      'User-Agent': 'system-server-daily-cron'
    }
  }).then(() => {
    console.log('success');
  }).catch(error => {
    console.error('Error updating player nectar:', error);
  });
}

processDaily();