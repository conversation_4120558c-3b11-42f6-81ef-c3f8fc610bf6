const axios = require("axios");
const { app_url } = require("../../config");

async function checkPlayerRewardLimit(waxId, rewardType) {
  try {
    const response = await axios.get(`${app_url}/player-limits/${waxId}/${rewardType}`, {
      headers: {
        'x-system-operation': 'true',
        'User-Agent': 'system-server-player-limit'
      }
    });
    const limitData = response.data;

    if (!limitData || limitData.length === 0) {
      return true; // Allow if no limit is set
    }

    const { current_count, max_limit, expires_at } = limitData[0];

    // Check if limit has expired and should be reset
    const now = new Date();
    const expiryDate = new Date(expires_at);

    if (now > expiryDate) {
      // Limit has expired, will be reset in general.js
      return true;
    }

    // Check if current count is below max limit
    const isUnderLimit = current_count < max_limit;
    return isUnderLimit;

  } catch (error) {
    console.error("Error checking player reward limit:", error);
    if (error.response) {
      console.error(`Error response:`, error.response.status, error.response.statusText);
    }
    return true; // Default to allowing rewards if there's an error
  }
}

async function updatePlayerRewardCount(waxId, rewardType, amount) {
  try {
    // Ensure amount is a number
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) {
      console.error(`Invalid amount value: ${amount}, not a number`);
      return;
    }

    const response = await axios.put(
      `${app_url}/player-limits/${waxId}/${rewardType}`,
      { amount: numericAmount },
      {
        headers: {
          "Content-Type": "application/json",
          'x-system-operation': 'true',
          'User-Agent': 'system-server-player-limit'
        },
      }
    );

    console.log(`Updated player count for ${waxId} and ${rewardType} by ${numericAmount}`);
  } catch (error) {
    console.error("Error updating player reward count:", error);
    if (error.response) {
      console.error(`Error response:`, error.response.status, error.response.statusText);
      console.error(`Error response data:`, error.response.data);
    }
  }
}

async function checkAndResetExpiredPlayerLimits() {
  try {
    const response = await axios.get(`${app_url}/player-limits`);
    const allLimits = response.data;

    const now = new Date();
    const expiredLimits = [];

    for (const limit of allLimits) {
      const expiryDate = new Date(limit.expires_at);

      if (now > expiryDate) {
        // Reset this limit
        await axios.post(
          `${app_url}/player-limits/reset/${limit.wax_id}/${limit.reward_type}`,
          {},
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        console.log(`Reset expired limit for player ${limit.wax_id} and ${limit.reward_type}`);

        // Add to list of expired limits
        expiredLimits.push({
          wax_id: limit.wax_id,
          reward_type: limit.reward_type
        });
      }
    }

    // Process rewards with "Player Limit" status for expired limits
    if (expiredLimits.length > 0) {
      try {
        // Get all rewards with "Player Limit" status
        const rewardsResponse = await axios.get(`${app_url}/rewards`);
        const allRewards = rewardsResponse.data;
        const playerLimitRewards = allRewards.filter(reward => reward.status === 'Player Limit');

        // Check each reward against expired limits
        for (const reward of playerLimitRewards) {
          const matchingLimit = expiredLimits.find(limit =>
            limit.wax_id === reward.wax_id && limit.reward_type === reward.type
          );

          if (matchingLimit) {
            // Import the updateRewardStatus function from adventure-rewards.js
            const { updateRewardStatus } = require('../../rewards/adventure-rewards');

            // Update reward status to "Disbursed"
            await updateRewardStatus(reward.event_id);
            console.log(`Updated reward ${reward.event_id} status from "Player Limit" to "Disbursed"`);
          }
        }
      } catch (rewardsError) {
        console.error("Error processing rewards with Player Limit status:", rewardsError);
      }
      return true; // Indicate that at least one player limit was reset
    } else {
      return false; // No player limits were reset
    }
  } catch (error) {
    console.error("Error checking and resetting expired player limits:", error);
    return false; // On error, treat as no reset
  }
}

module.exports = {
  checkPlayerRewardLimit,
  updatePlayerRewardCount,
  checkAndResetExpiredPlayerLimits
};