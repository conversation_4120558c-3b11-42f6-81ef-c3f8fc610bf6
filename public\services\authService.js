class AuthService {
  static async login() {
    try {
      const userAccount = await wax.login();
      window.userAccount = userAccount;  
      const isValidSession = await this.validateUser(userAccount);
      if (!isValidSession) {
        throw new Error(`Failed to validate session for user: ${userAccount}`);
      }
      return userAccount;
    } catch (error) {
      console.error("Authentication error:", error.message);
      throw error;
    }
  }

  static async validateUser(userAccount) {
    try {
      const pubKey = wax.pubKeys[0];
      const url = `${domain_url}/sessions/user/${userAccount}`;
      const headers = {
        'Authorization': pubKey,
        'Content-Type': 'application/json',
      };
      console.debug('[validateUser] userAccount:', userAccount);
      console.debug('[validateUser] pubKey:', pubKey);
      console.debug('[validateUser] url:', url);
      console.debug('[validateUser] headers:', headers);
      console.debug('[validateUser] REQUEST: { method: GET, url:', url, ', headers:', headers, '}');

      const response = await axios.get(url, { headers });
      console.debug('[validateUser] response status:', response.status);
      console.debug('[validateUser] response data:', response.data);

      if (response.status === 200) {
        if (response.data && response.data[0] && response.data[0].token) {
          const rawToken = response.data[0].token;
          sessionToken = rawToken;
          console.debug('[validateUser] sessionToken set:', sessionToken);
          return true;
        } else {
          console.error('ERROR: validateUser - Token not found in response', response.data);
        }
      }

      console.error('ERROR: validateUser - Error during validation:', response.status, response.data);
      try {
        console.debug('[validateUser] Attempting to create session for user:', userAccount);
        await this.createSessionForUser(userAccount);
        return await this.validateUser(userAccount);
      } catch (createError) {
        console.error('ERROR: validateUser - Failed to create session:', createError, createError?.stack);
        return false;
      }
    } catch (error) {
      console.error('ERROR: validateUser - Exception:', error.message, error?.stack);
      console.debug('[validateUser] Exception details:', {
        userAccount,
        pubKey: wax.pubKeys[0],
        domain_url,
        error: error.toString(),
        stack: error?.stack
      });
      try {
        console.debug('[validateUser] Attempting to create session for user after exception:', userAccount);
        await this.createSessionForUser(userAccount);
        return await this.validateUser(userAccount);
      } catch (createError) {
        console.error('ERROR: validateUser - Failed to create session after error:', createError, createError?.stack);
        return false;
      }
    }
  }

  static async createSessionForUser(userAccount) {
    try {
      const pubKey = wax.pubKeys[0];
      if (!pubKey) {
        console.error('[createSessionForUser] Public key not available for user:', userAccount);
        throw new Error('Public key not available');
      }
      const postUrl = `${domain_url}/sessions`;
      const postData = {
        user: userAccount,
        token: pubKey
      };
      console.debug('[createSessionForUser] userAccount:', userAccount);
      console.debug('[createSessionForUser] pubKey:', pubKey);
      console.debug('[createSessionForUser] postUrl:', postUrl);
      console.debug('[createSessionForUser] postData:', postData);
      console.debug('[createSessionForUser] REQUEST: { method: POST, url:', postUrl, ', headers: (default axios), data:', postData, '}');

      const response = await axios.post(postUrl, postData);
      console.debug('[createSessionForUser] response status:', response.status);
      console.debug('[createSessionForUser] response data:', response.data);

      if (response.status === 200 || response.status === 201) {
        return response.data;
      } else {
        console.error('ERROR: createSessionForUser - Failed to create session:', response.status, response.data);
        throw new Error('Failed to create session');
      }
    } catch (error) {
      console.error('ERROR: createSessionForUser - Exception:', error.message, error?.stack);
      console.debug('[createSessionForUser] Exception details:', {
        userAccount,
        pubKey: wax.pubKeys[0],
        domain_url,
        error: error.toString(),
        stack: error?.stack
      });
      throw error;
    }
  }
}

async function login() {
  try {
    const userAccount = await AuthService.login();
    UIService.showAuthenticatedUI(userAccount);
    
    // Initialize player options after successful login
    if (typeof initializePlayerOptionsOnLogin === 'function') {
      initializePlayerOptionsOnLogin(userAccount);
    }
    
    await DataService.fetchInitialData(userAccount);
    await displayWorlds();
    mainTimer = DataService.setupPeriodicUpdates(userAccount);

    if (sessionToken && !sessionToken.startsWith('Bearer ')) {
      sessionToken = `Bearer ${sessionToken}`;
    }

    return sessionToken;
  } catch (error) {
    console.error('ERROR: Login process error:', error);
    showAlert(error.message);
    throw error;
  }
}